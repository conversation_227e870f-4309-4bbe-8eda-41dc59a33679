Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.56f1 (dd0c98481d00) revision 14486680'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 7597 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.56f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Unity/Game Projects/GAME DEV PROJECT
-logFile
Logs/AssetImportWorker0.log
-srvPort
50713
Successfully changed project path to: C:/Unity/Game Projects/GAME DEV PROJECT
C:/Unity/Game Projects/GAME DEV PROJECT
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20500]  Target information:

Player connection [20500]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 803710367 [EditorId] 803710367 [Version] 1048832 [Id] WindowsEditor(7,Genki) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20500]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 803710367 [EditorId] 803710367 [Version] 1048832 [Id] WindowsEditor(7,Genki) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20500] Host joined multi-casting on [***********:54997]...
Player connection [20500] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 203.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.56f1 (dd0c98481d00)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.56f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/Game Projects/GAME DEV PROJECT/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3050 Laptop GPU (ID=0x25a2)
    Vendor:   NVIDIA
    VRAM:     3964 MB
    Driver:   32.0.15.6636
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.56f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.56f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.56f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56676
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.56f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.011487 seconds.
- Loaded All Assemblies, in  0.621 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.384 seconds
Domain Reload Profiling: 1004ms
	BeginReloadAssembly (309ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (77ms)
	LoadAllAssembliesAndSetupDomain (177ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (174ms)
			TypeCache.Refresh (171ms)
				TypeCache.ScanAssembly (153ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (385ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (320ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (14ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (4ms)
			ProcessInitializeOnLoadAttributes (222ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.222 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.008 seconds
Domain Reload Profiling: 2215ms
	BeginReloadAssembly (194ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (912ms)
		LoadAssemblies (711ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (313ms)
			TypeCache.Refresh (264ms)
				TypeCache.ScanAssembly (239ms)
			ScanForSourceGeneratedMonoScriptInfo (33ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1008ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (783ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (561ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 8.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5682 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.1 MB). Loaded Objects now: 6165.
Memory consumption went from 252.8 MB to 251.6 MB.
Total: 4.962700 ms (FindLiveObjects: 0.563400 ms CreateObjectMapping: 0.332700 ms MarkObjects: 3.496400 ms  DeleteObjects: 0.569000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1569.917573 seconds.
  path: Assets/Wasteland Cabin
  artifactKey: Guid(0c5b4e1bf45ef094baa2a069779f8b89) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Wasteland Cabin using Guid(0c5b4e1bf45ef094baa2a069779f8b89) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '29afeefda51cf91484582d653a5ce81b') in 0.003460 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 15.806890 seconds.
  path: Assets/Wasteland Cabin/Models/Wast_Antlers.fbx
  artifactKey: Guid(31fa931e7f5630d468a3b5f5b873cf3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Wasteland Cabin/Models/Wast_Antlers.fbx using Guid(31fa931e7f5630d468a3b5f5b873cf3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'f77ec9b1729c306fc9c8d2bf1ea77b36') in 0.318852 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 24
========================================================================
Received Import Request.
  Time since last request: 78.407191 seconds.
  path: Assets/Wasteland Cabin/Show.unity
  artifactKey: Guid(09c73d1f625a05b4cb0e8aadb72e430f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Wasteland Cabin/Show.unity using Guid(09c73d1f625a05b4cb0e8aadb72e430f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '271af7658d141ec38e23d2e75e25f7a2') in 0.001376 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.317205 seconds.
  path: Assets/Wasteland Cabin/Read me.txt
  artifactKey: Guid(9fff5677ce4950f48a14234c14406d00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Wasteland Cabin/Read me.txt using Guid(9fff5677ce4950f48a14234c14406d00) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '41aef77cbaea07917d847ed36664b6ef') in 0.001658 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 4.653865 seconds.
  path: Assets/Wasteland Cabin/Show/Lightmap-0_comp_light.exr
  artifactKey: Guid(125ef381e79aca34fa3d6ec37b8e7810) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Wasteland Cabin/Show/Lightmap-0_comp_light.exr using Guid(125ef381e79aca34fa3d6ec37b8e7810) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'a38248a09f0bbfa5dfd389ba3948d661') in 0.042818 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 23.593181 seconds.
  path: Assets/Wasteland Cabin/Show/Lightmap-0_comp_shadowmask.png
  artifactKey: Guid(3362f8584d3681547bb271e6482cbf10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Wasteland Cabin/Show/Lightmap-0_comp_shadowmask.png using Guid(3362f8584d3681547bb271e6482cbf10) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '26e6652c22ef96e9603d4243a322eb9a') in 0.041091 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 2.533448 seconds.
  path: Assets/Wasteland Cabin/Show/Lightmap-0_comp_dir.png
  artifactKey: Guid(61164cc7d76c47548a22bd880d638d58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Wasteland Cabin/Show/Lightmap-0_comp_dir.png using Guid(61164cc7d76c47548a22bd880d638d58) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '8a444a9cd320b3d37f2fee4990518860') in 0.018395 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.873 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.950 seconds
Domain Reload Profiling: 4819ms
	BeginReloadAssembly (248ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (528ms)
		LoadAssemblies (620ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (3951ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (920ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (648ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 10.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.1 MB). Loaded Objects now: 6195.
Memory consumption went from 219.3 MB to 218.2 MB.
Total: 5.945100 ms (FindLiveObjects: 0.764500 ms CreateObjectMapping: 0.501200 ms MarkObjects: 4.086700 ms  DeleteObjects: 0.590300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 150.783938 seconds.
  path: Assets/Wasteland Cabin/Prefabs/Wasteland_Cabin_Empty.prefab
  artifactKey: Guid(5fcfdca045ca3484baf910aae6bffc54) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Wasteland Cabin/Prefabs/Wasteland_Cabin_Empty.prefab using Guid(5fcfdca045ca3484baf910aae6bffc54) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '476110dba163a6a2e91618a079d9a473') in 0.510815 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 71
========================================================================
Received Import Request.
  Time since last request: 17.769928 seconds.
  path: Assets/Zombie_Mutant/mesh/SKM_Zombie_Mutant.fbx
  artifactKey: Guid(930d3f7f8ec96db42afe5c193a99d837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Zombie_Mutant/mesh/SKM_Zombie_Mutant.fbx using Guid(930d3f7f8ec96db42afe5c193a99d837) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '0fc77d2db8214054752d1febafe66882') in 1.111289 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 148
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.984 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.782 seconds
Domain Reload Profiling: 3765ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (690ms)
		LoadAssemblies (769ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (2782ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (663ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (462ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 6.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.1 MB). Loaded Objects now: 6212.
Memory consumption went from 221.7 MB to 220.7 MB.
Total: 8.131800 ms (FindLiveObjects: 0.660700 ms CreateObjectMapping: 0.426800 ms MarkObjects: 6.293600 ms  DeleteObjects: 0.748900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.550 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.986 seconds
Domain Reload Profiling: 4542ms
	BeginReloadAssembly (648ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (272ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (810ms)
		LoadAssemblies (894ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (57ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (2986ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (589ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (401ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 6.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5519 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.1 MB). Loaded Objects now: 6228.
Memory consumption went from 221.8 MB to 220.7 MB.
Total: 7.206000 ms (FindLiveObjects: 0.636600 ms CreateObjectMapping: 0.625200 ms MarkObjects: 4.902300 ms  DeleteObjects: 1.040400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: b12694177d6a4a087b5db9a1fd3a93b1 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 12766a56e781cf519d3a460d3e42b094 -> 8d4ac026797ee5985dd7fa5b52cacd40
========================================================================
Received Import Request.
  Time since last request: 760.130232 seconds.
  path: Assets/FollowScript1.cs
  artifactKey: Guid(d6c042cdebff67f4ba0b0833bb085a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/FollowScript1.cs using Guid(d6c042cdebff67f4ba0b0833bb085a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '397a595f9b4c0fa301fbc135b55a3853') in 0.009579 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.741 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.061 seconds
Domain Reload Profiling: 3797ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (470ms)
		LoadAssemblies (509ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (66ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (30ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (3061ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (646ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (443ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 7.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5519 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.1 MB). Loaded Objects now: 6244.
Memory consumption went from 221.6 MB to 220.5 MB.
Total: 6.359100 ms (FindLiveObjects: 0.757300 ms CreateObjectMapping: 0.261100 ms MarkObjects: 4.340000 ms  DeleteObjects: 0.998100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: b12694177d6a4a087b5db9a1fd3a93b1 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/FollowScript1.cs: 9c620000ce1eb41733ce7d78911e1540 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: bba1b69276a3c420eee2cfc3ad20013d -> 8d4ac026797ee5985dd7fa5b52cacd40
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.681 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.687 seconds
Domain Reload Profiling: 2364ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (382ms)
		LoadAssemblies (436ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (58ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (28ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (665ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (464ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 7.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5519 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (1.1 MB). Loaded Objects now: 6258.
Memory consumption went from 221.8 MB to 220.7 MB.
Total: 8.198600 ms (FindLiveObjects: 0.794400 ms CreateObjectMapping: 0.354000 ms MarkObjects: 5.787800 ms  DeleteObjects: 1.259300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: b12694177d6a4a087b5db9a1fd3a93b1 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/FollowScript1.cs: 9c620000ce1eb41733ce7d78911e1540 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 12766a56e781cf519d3a460d3e42b094 -> 8d4ac026797ee5985dd7fa5b52cacd40
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.666 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.550 seconds
Domain Reload Profiling: 2213ms
	BeginReloadAssembly (194ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (384ms)
		LoadAssemblies (428ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (59ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (30ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1551ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (407ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 6.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (1.1 MB). Loaded Objects now: 6272.
Memory consumption went from 221.8 MB to 220.7 MB.
Total: 5.430900 ms (FindLiveObjects: 0.670000 ms CreateObjectMapping: 0.210400 ms MarkObjects: 3.766900 ms  DeleteObjects: 0.781000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: b12694177d6a4a087b5db9a1fd3a93b1 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/FollowScript1.cs: 9c620000ce1eb41733ce7d78911e1540 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.653 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.455 seconds
Domain Reload Profiling: 2103ms
	BeginReloadAssembly (180ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (388ms)
		LoadAssemblies (425ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (63ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (30ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1455ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (558ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (378ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 5.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5519 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.1 MB). Loaded Objects now: 6288.
Memory consumption went from 221.8 MB to 220.7 MB.
Total: 7.334900 ms (FindLiveObjects: 0.866800 ms CreateObjectMapping: 0.478700 ms MarkObjects: 4.710800 ms  DeleteObjects: 1.275900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: b12694177d6a4a087b5db9a1fd3a93b1 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/FollowScript1.cs: 9c620000ce1eb41733ce7d78911e1540 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 12766a56e781cf519d3a460d3e42b094 -> 8d4ac026797ee5985dd7fa5b52cacd40
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.454 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.435 seconds
Domain Reload Profiling: 9908ms
	BeginReloadAssembly (1721ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (778ms)
	RebuildCommonClasses (113ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (141ms)
	LoadAllAssembliesAndSetupDomain (2475ms)
		LoadAssemblies (2804ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (106ms)
				TypeCache.ScanAssembly (5ms)
			ScanForSourceGeneratedMonoScriptInfo (145ms)
			ResolveRequiredComponents (46ms)
	FinalizeReload (5436ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (979ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (727ms)
			ProcessInitializeOnLoadMethodAttributes (108ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5519 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.1 MB). Loaded Objects now: 6303.
Memory consumption went from 221.8 MB to 220.7 MB.
Total: 9.753800 ms (FindLiveObjects: 1.216000 ms CreateObjectMapping: 0.840000 ms MarkObjects: 6.902300 ms  DeleteObjects: 0.793800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: c9ec24e01972d2ed5e0b62202001e186 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/FollowScript1.cs: 9c620000ce1eb41733ce7d78911e1540 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: e125239a3495f9b96388ab7193f39293 -> 8d4ac026797ee5985dd7fa5b52cacd40
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.409 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.419 seconds
Domain Reload Profiling: 4823ms
	BeginReloadAssembly (420ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (853ms)
		LoadAssemblies (1021ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (97ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (59ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (3420ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (685ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (478ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 9.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5520 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.1 MB). Loaded Objects now: 6319.
Memory consumption went from 221.9 MB to 220.8 MB.
Total: 11.082000 ms (FindLiveObjects: 0.902200 ms CreateObjectMapping: 0.632600 ms MarkObjects: 6.817000 ms  DeleteObjects: 2.724600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: c9ec24e01972d2ed5e0b62202001e186 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/FollowScript1.cs: 9c620000ce1eb41733ce7d78911e1540 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: ea51e9e4de5749db0cef4cfb0f0bfe85 -> 8d4ac026797ee5985dd7fa5b52cacd40
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.765 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.567 seconds
Domain Reload Profiling: 5325ms
	BeginReloadAssembly (220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (82ms)
	LoadAllAssembliesAndSetupDomain (1378ms)
		LoadAssemblies (1337ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (73ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (61ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (3568ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (674ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (457ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 16.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5519 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (1.1 MB). Loaded Objects now: 6333.
Memory consumption went from 221.8 MB to 220.7 MB.
Total: 8.811100 ms (FindLiveObjects: 0.932700 ms CreateObjectMapping: 0.492800 ms MarkObjects: 6.381500 ms  DeleteObjects: 1.001600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: c9ec24e01972d2ed5e0b62202001e186 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/FollowScript1.cs: 9c620000ce1eb41733ce7d78911e1540 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: e125239a3495f9b96388ab7193f39293 -> 8d4ac026797ee5985dd7fa5b52cacd40
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.971 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.579 seconds
Domain Reload Profiling: 5546ms
	BeginReloadAssembly (238ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (617ms)
		LoadAssemblies (714ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (38ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (4580ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1175ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (171ms)
			ProcessInitializeOnLoadAttributes (863ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 13.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5519 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.1 MB). Loaded Objects now: 6348.
Memory consumption went from 221.9 MB to 220.8 MB.
Total: 9.139200 ms (FindLiveObjects: 1.292100 ms CreateObjectMapping: 1.227400 ms MarkObjects: 5.838500 ms  DeleteObjects: 0.778400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/FollowScript.cs: c9ec24e01972d2ed5e0b62202001e186 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/FollowScript1.cs: 9c620000ce1eb41733ce7d78911e1540 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: e125239a3495f9b96388ab7193f39293 -> 8d4ac026797ee5985dd7fa5b52cacd40

using UnityEngine;

public class FollowTarget : MonoBehaviour
{
    public Transform target;        // The target to follow (e.g., the player)
    public Vector3 offset = new Vector3(0, 5, -10); // Offset from the target
    public float followSpeed = 5f;  // How fast the camera follows

    void LateUpdate()
    {
        if (target == null)
        {
            Debug.LogWarning("FollowTarget: No target assigned.");
            return;
        }

        // Desired position based on target and offset
        Vector3 desiredPosition = target.position + offset;

        // Smoothly interpolate between current position and desired position
        transform.position = Vector3.Lerp(transform.position, desiredPosition, followSpeed * Time.deltaTime);

        // Optional: Make the camera look at the target
        transform.LookAt(target);
    }
}
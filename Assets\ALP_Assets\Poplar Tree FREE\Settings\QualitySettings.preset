%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: QualitySettings
  m_TargetType:
    m_NativeTypeID: 47
    m_ManagedTypePPtr: {fileID: 0}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_CurrentQuality
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.size
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].name
    value: Ultra
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].pixelLightCount
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadows
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowResolution
    value: 3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowProjection
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowCascades
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowDistance
    value: 300
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowNearPlaneOffset
    value: 3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowCascade2Split
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowCascade4Split.x
    value: 0.12474708
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowCascade4Split.y
    value: 0.24721439
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowCascade4Split.z
    value: 0.43861288
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].shadowmaskMode
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].skinWeights
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].textureQuality
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].anisotropicTextures
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].antiAliasing
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].softParticles
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].softVegetation
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].realtimeReflectionProbes
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].billboardsFaceCameraPosition
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].vSyncCount
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].lodBias
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].maximumLODLevel
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].streamingMipmapsActive
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].streamingMipmapsAddAllCameras
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].streamingMipmapsMemoryBudget
    value: 512
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].streamingMipmapsRenderersPerFrame
    value: 512
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].streamingMipmapsMaxLevelReduction
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].streamingMipmapsMaxFileIORequests
    value: 1024
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].particleRaycastBudget
    value: 4096
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].asyncUploadTimeSlice
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].asyncUploadBufferSize
    value: 16
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].asyncUploadPersistentBuffer
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].resolutionScalingFixedDPIFactor
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].customRenderPipeline
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_QualitySettings.Array.data[0].excludedTargetPlatforms.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.size
    value: 14
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[0].first
    value: Android
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[0].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[1].first
    value: Lumin
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[1].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[2].first
    value: Nintendo 3DS
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[2].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[3].first
    value: Nintendo Switch
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[3].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[4].first
    value: PS4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[4].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[5].first
    value: PSP2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[5].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[6].first
    value: Server
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[6].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[7].first
    value: Stadia
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[7].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[8].first
    value: Standalone
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[8].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[9].first
    value: WebGL
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[9].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[10].first
    value: Windows Store Apps
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[10].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[11].first
    value: XboxOne
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[11].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[12].first
    value: iPhone
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[12].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[13].first
    value: tvOS
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PerPlatformDefaultQuality.Array.data[13].second
    value: 0
    objectReference: {fileID: 0}
  m_ExcludedProperties: []

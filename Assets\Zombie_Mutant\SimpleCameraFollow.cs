using UnityEngine;

public class SimpleCameraFollow : MonoBehaviour
{
    [Header("Camera Settings")]
    public Transform cameraToFollow;
    public bool autoFindMainCamera = true;
    
    [Header("Follow Behavior")]
    public Vector3 offset = new Vector3(0, 0, -3); // Position offset from camera
    public float followSpeed = 5f;
    public bool smoothFollow = true;
    
    [Header("Rotation Settings")]
    public bool followCameraRotation = false;
    public bool lookAtCamera = true;
    public float rotationSpeed = 3f;
    
    [Header("Distance Control")]
    public float minDistance = 1f;
    public float maxDistance = 10f;
    public bool teleportIfTooFar = true;
    public float teleportDistance = 20f;
    
    private Camera mainCamera;
    
    void Start()
    {
        // Auto-find the main camera if not assigned
        if (cameraToFollow == null && autoFindMainCamera)
        {
            FindMainCamera();
        }
    }
    
    void Update()
    {
        // Try to find camera again if we don't have one
        if (cameraToFollow == null && autoFindMainCamera)
        {
            FindMainCamera();
            return;
        }
        
        if (cameraToFollow == null)
        {
            Debug.LogWarning($"{gameObject.name}: No camera to follow!");
            return;
        }
        
        FollowCamera();
    }
    
    void FollowCamera()
    {
        // Calculate desired position
        Vector3 desiredPosition = cameraToFollow.position + cameraToFollow.TransformDirection(offset);
        
        // Check distance to camera
        float distanceToCamera = Vector3.Distance(transform.position, cameraToFollow.position);
        
        // Teleport if too far away
        if (teleportIfTooFar && distanceToCamera > teleportDistance)
        {
            transform.position = desiredPosition;
            Debug.Log($"{gameObject.name} teleported to camera (was too far: {distanceToCamera:F1}m)");
            return;
        }
        
        // Apply distance constraints
        if (distanceToCamera < minDistance)
        {
            // Too close - move away from camera
            Vector3 directionAway = (transform.position - cameraToFollow.position).normalized;
            desiredPosition = cameraToFollow.position + directionAway * minDistance;
        }
        else if (distanceToCamera > maxDistance)
        {
            // Too far - move closer to camera
            Vector3 directionToCamera = (cameraToFollow.position - transform.position).normalized;
            desiredPosition = transform.position + directionToCamera * (distanceToCamera - maxDistance);
        }
        
        // Move to desired position
        if (smoothFollow)
        {
            transform.position = Vector3.Lerp(transform.position, desiredPosition, followSpeed * Time.deltaTime);
        }
        else
        {
            transform.position = desiredPosition;
        }
        
        // Handle rotation
        HandleRotation();
    }
    
    void HandleRotation()
    {
        if (followCameraRotation)
        {
            // Follow the camera's rotation
            if (smoothFollow)
            {
                transform.rotation = Quaternion.Slerp(transform.rotation, cameraToFollow.rotation, rotationSpeed * Time.deltaTime);
            }
            else
            {
                transform.rotation = cameraToFollow.rotation;
            }
        }
        else if (lookAtCamera)
        {
            // Look at the camera
            Vector3 directionToCamera = (cameraToFollow.position - transform.position).normalized;
            if (directionToCamera != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(directionToCamera);
                if (smoothFollow)
                {
                    transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
                }
                else
                {
                    transform.rotation = targetRotation;
                }
            }
        }
    }
    
    void FindMainCamera()
    {
        // Try Camera.main first
        mainCamera = Camera.main;
        if (mainCamera != null)
        {
            cameraToFollow = mainCamera.transform;
            Debug.Log($"{gameObject.name}: Found main camera.");
            return;
        }
        
        // Try finding by tag
        GameObject cameraObj = GameObject.FindGameObjectWithTag("MainCamera");
        if (cameraObj != null)
        {
            mainCamera = cameraObj.GetComponent<Camera>();
            if (mainCamera != null)
            {
                cameraToFollow = mainCamera.transform;
                Debug.Log($"{gameObject.name}: Found camera by MainCamera tag.");
                return;
            }
        }
        
        // Try finding any camera in the scene
        Camera[] cameras = FindObjectsOfType<Camera>();
        if (cameras.Length > 0)
        {
            mainCamera = cameras[0];
            cameraToFollow = mainCamera.transform;
            Debug.Log($"{gameObject.name}: Found camera: {mainCamera.name}");
            return;
        }
        
        Debug.LogWarning($"{gameObject.name}: Could not find any camera in the scene!");
    }
    
    // Public methods for runtime control
    public void SetCameraTarget(Transform newCamera)
    {
        cameraToFollow = newCamera;
        if (newCamera != null)
        {
            Debug.Log($"{gameObject.name}: Camera target set to {newCamera.name}");
        }
    }
    
    public void SetOffset(Vector3 newOffset)
    {
        offset = newOffset;
    }
    
    public void SetFollowSpeed(float speed)
    {
        followSpeed = Mathf.Max(0.1f, speed);
    }
    
    public void ToggleSmoothFollow()
    {
        smoothFollow = !smoothFollow;
    }
    
    public void ToggleLookAtCamera()
    {
        lookAtCamera = !lookAtCamera;
        followCameraRotation = false; // Disable rotation following when looking at camera
    }
    
    public void ToggleFollowRotation()
    {
        followCameraRotation = !followCameraRotation;
        lookAtCamera = false; // Disable look at when following rotation
    }
    
    // Teleport to camera immediately
    public void TeleportToCamera()
    {
        if (cameraToFollow != null)
        {
            Vector3 teleportPosition = cameraToFollow.position + cameraToFollow.TransformDirection(offset);
            transform.position = teleportPosition;
            Debug.Log($"{gameObject.name}: Teleported to camera position.");
        }
    }
    
    // Get current distance to camera
    public float GetDistanceToCamera()
    {
        if (cameraToFollow != null)
        {
            return Vector3.Distance(transform.position, cameraToFollow.position);
        }
        return 0f;
    }
    
    // Check if camera is in range
    public bool IsCameraInRange()
    {
        if (cameraToFollow == null) return false;
        
        float distance = GetDistanceToCamera();
        return distance >= minDistance && distance <= maxDistance;
    }
    
    // Gizmos for visual debugging in Scene view
    void OnDrawGizmosSelected()
    {
        if (cameraToFollow != null)
        {
            // Draw line to camera
            Gizmos.color = Color.cyan;
            Gizmos.DrawLine(transform.position, cameraToFollow.position);
            
            // Draw desired position
            Vector3 desiredPos = cameraToFollow.position + cameraToFollow.TransformDirection(offset);
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(desiredPos, 0.3f);
            
            // Draw distance ranges
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(cameraToFollow.position, minDistance);
            
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(cameraToFollow.position, maxDistance);
            
            if (teleportIfTooFar)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(cameraToFollow.position, teleportDistance);
            }
            
            // Draw offset direction
            Gizmos.color = Color.magenta;
            Gizmos.DrawRay(cameraToFollow.position, cameraToFollow.TransformDirection(offset));
        }
        else if (autoFindMainCamera)
        {
            // Show that we're looking for a camera
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, 1f);
        }
    }
    
    // Display info in inspector
    void OnDrawGizmos()
    {
        if (cameraToFollow != null)
        {
            // Show current distance
            float distance = Vector3.Distance(transform.position, cameraToFollow.position);
            
            // Color code based on distance
            if (distance < minDistance)
                Gizmos.color = Color.red;
            else if (distance > maxDistance)
                Gizmos.color = Color.yellow;
            else
                Gizmos.color = Color.green;
                
            Gizmos.DrawWireSphere(transform.position, 0.5f);
        }
    }
}

﻿
////// What does this Lighting settings do? ////// 
If you care about good lighting in unity this tool is for you
In BIRP by default Unity sets gamma correction to light color incorrectly. 
to set it correctly its only possible to do thru script    

Use Linear Intensity:
When this is enabled, sets Unity lighting lightsUseLinearIntensity = True
https://docs.unity3d.com/ScriptReference/Rendering.GraphicsSettings-lightsUseLinearIntensity.html


Use Color Temperature:
When this is enabled, sets Unity lighting to use temperature mode for the color of light
Checking this will add a temperature field and change color to filter
https://docs.unity3d.com/ScriptReference/Rendering.GraphicsSettings-lightsUseColorTemperature.html

 

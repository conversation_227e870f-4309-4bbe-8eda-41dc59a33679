using UnityEngine;
using StarterAssets;

public class CameraFollowScript : MonoBehaviour
{
    [Header("Target Settings")]
    public Transform target;
    public bool autoFindPlayer = true;
    
    [Header("Camera Follow Settings")]
    public Vector3 offset = new Vector3(0, 2, -5);
    public float followSpeed = 10f;
    public float rotationSpeed = 5f;
    
    [Header("Camera Behavior")]
    public bool smoothFollow = true;
    public bool lookAtTarget = true;
    public bool followRotation = false; // Follow player's rotation
    
    [Header("Distance Control")]
    public float minDistance = 2f;
    public float maxDistance = 10f;
    public bool maintainDistance = true;
    
    [Header("Collision Detection")]
    public bool avoidObstacles = true;
    public LayerMask obstacleLayer = -1;
    public float collisionBuffer = 0.3f;
    
    private Vector3 currentVelocity;
    private Vector3 lastTargetPosition;
    
    void Start()
    {
        if (target == null && autoFindPlayer)
        {
            FindPlayer();
        }
        
        if (target != null)
        {
            lastTargetPosition = target.position;
        }
    }
    
    void LateUpdate()
    {
        if (target == null)
        {
            if (autoFindPlayer)
            {
                FindPlayer();
            }
            return;
        }
        
        UpdateCameraPosition();
        
        if (lookAtTarget)
        {
            UpdateCameraRotation();
        }
        
        lastTargetPosition = target.position;
    }
    
    void UpdateCameraPosition()
    {
        Vector3 desiredPosition;
        
        if (followRotation)
        {
            // Follow player's rotation for the offset
            desiredPosition = target.position + target.TransformDirection(offset);
        }
        else
        {
            // Use world space offset
            desiredPosition = target.position + offset;
        }
        
        // Handle collision detection
        if (avoidObstacles)
        {
            desiredPosition = HandleCollisions(desiredPosition);
        }
        
        // Apply distance constraints
        if (maintainDistance)
        {
            desiredPosition = ApplyDistanceConstraints(desiredPosition);
        }
        
        // Move camera
        if (smoothFollow)
        {
            transform.position = Vector3.SmoothDamp(transform.position, desiredPosition, ref currentVelocity, 1f / followSpeed);
        }
        else
        {
            transform.position = Vector3.Lerp(transform.position, desiredPosition, followSpeed * Time.deltaTime);
        }
    }
    
    void UpdateCameraRotation()
    {
        Vector3 direction = (target.position - transform.position).normalized;
        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }
    }
    
    Vector3 HandleCollisions(Vector3 desiredPosition)
    {
        Vector3 directionToTarget = (desiredPosition - target.position).normalized;
        float distanceToTarget = Vector3.Distance(target.position, desiredPosition);
        
        RaycastHit hit;
        if (Physics.Raycast(target.position, directionToTarget, out hit, distanceToTarget, obstacleLayer))
        {
            // Move camera closer to avoid obstacle
            Vector3 safePosition = hit.point - directionToTarget * collisionBuffer;
            return safePosition;
        }
        
        return desiredPosition;
    }
    
    Vector3 ApplyDistanceConstraints(Vector3 desiredPosition)
    {
        float currentDistance = Vector3.Distance(target.position, desiredPosition);
        
        if (currentDistance < minDistance)
        {
            Vector3 direction = (desiredPosition - target.position).normalized;
            return target.position + direction * minDistance;
        }
        else if (currentDistance > maxDistance)
        {
            Vector3 direction = (desiredPosition - target.position).normalized;
            return target.position + direction * maxDistance;
        }
        
        return desiredPosition;
    }
    
    void FindPlayer()
    {
        // Try to find player by tag
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            target = player.transform;
            Debug.Log("CameraFollow: Found player by tag.");
            return;
        }
        
        // Try to find FirstPersonController
        FirstPersonController fpsController = FindObjectOfType<FirstPersonController>();
        if (fpsController != null)
        {
            target = fpsController.transform;
            Debug.Log("CameraFollow: Found FirstPersonController.");
            return;
        }
        
        // Try to find by name
        player = GameObject.Find("Player");
        if (player != null)
        {
            target = player.transform;
            Debug.Log("CameraFollow: Found player by name.");
            return;
        }
        
        Debug.LogWarning("CameraFollow: Could not find player automatically.");
    }
    
    // Public methods for runtime control
    public void SetTarget(Transform newTarget)
    {
        target = newTarget;
        if (target != null)
        {
            lastTargetPosition = target.position;
        }
    }
    
    public void SetOffset(Vector3 newOffset)
    {
        offset = newOffset;
    }
    
    public void SetFollowSpeed(float speed)
    {
        followSpeed = speed;
    }
    
    // Gizmos for debugging
    void OnDrawGizmosSelected()
    {
        if (target != null)
        {
            // Draw connection line
            Gizmos.color = Color.cyan;
            Gizmos.DrawLine(transform.position, target.position);
            
            // Draw desired position
            Vector3 desiredPos = followRotation ? 
                target.position + target.TransformDirection(offset) : 
                target.position + offset;
            
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(desiredPos, 0.3f);
            
            // Draw distance constraints
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(target.position, minDistance);
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(target.position, maxDistance);
        }
    }
}

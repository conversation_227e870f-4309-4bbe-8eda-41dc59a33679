using UnityEngine;
using UnityEngine.AI;
using StarterAssets;

[RequireComponent(typeof(NavMeshAgent))]
public class AIFollowScript : MonoBehaviour
{
    [Header("Target Settings")]
    public Transform target;
    public bool autoFindPlayer = true;
    
    [Header("Follow Behavior")]
    public float followDistance = 3f;
    public float stopDistance = 1.5f;
    public float maxFollowDistance = 15f; // Teleport if too far
    
    [Header("AI Behavior")]
    public bool useNavMesh = true;
    public float updateRate = 0.1f; // How often to update path
    public bool faceTarget = true;
    public float rotationSpeed = 5f;
    
    [Header("Advanced Settings")]
    public bool canTeleport = true;
    public float teleportDistance = 20f;
    public bool waitForPlayer = true;
    public float waitTime = 2f;
    
    private NavMeshAgent agent;
    private float lastUpdateTime;
    private float waitTimer;
    private bool isWaiting;
    private Vector3 lastTargetPosition;
    
    void Start()
    {
        agent = GetComponent<NavMeshAgent>();
        
        if (target == null && autoFindPlayer)
        {
            FindPlayer();
        }
        
        if (target != null)
        {
            lastTargetPosition = target.position;
        }
        
        // Configure NavMeshAgent
        if (agent != null)
        {
            agent.stoppingDistance = stopDistance;
        }
    }
    
    void Update()
    {
        if (target == null)
        {
            if (autoFindPlayer)
            {
                FindPlayer();
            }
            return;
        }
        
        float distanceToTarget = Vector3.Distance(transform.position, target.position);
        
        // Check if we need to teleport
        if (canTeleport && distanceToTarget > teleportDistance)
        {
            TeleportToTarget();
            return;
        }
        
        // Update follow behavior
        if (Time.time - lastUpdateTime >= updateRate)
        {
            UpdateFollowBehavior(distanceToTarget);
            lastUpdateTime = Time.time;
        }
        
        // Handle rotation
        if (faceTarget && !agent.pathPending && agent.remainingDistance < 0.5f)
        {
            FaceTarget();
        }
        
        // Update waiting behavior
        if (waitForPlayer)
        {
            UpdateWaitBehavior();
        }
        
        lastTargetPosition = target.position;
    }
    
    void UpdateFollowBehavior(float distanceToTarget)
    {
        if (useNavMesh && agent != null)
        {
            // Use NavMesh for pathfinding
            if (distanceToTarget > followDistance)
            {
                agent.SetDestination(target.position);
                isWaiting = false;
                waitTimer = 0f;
            }
            else if (distanceToTarget <= stopDistance)
            {
                agent.ResetPath();
                if (waitForPlayer)
                {
                    isWaiting = true;
                }
            }
        }
        else
        {
            // Simple direct movement (no NavMesh)
            if (distanceToTarget > followDistance)
            {
                Vector3 direction = (target.position - transform.position).normalized;
                transform.position += direction * agent.speed * Time.deltaTime;
                isWaiting = false;
                waitTimer = 0f;
            }
            else if (distanceToTarget <= stopDistance && waitForPlayer)
            {
                isWaiting = true;
            }
        }
    }
    
    void UpdateWaitBehavior()
    {
        if (isWaiting)
        {
            waitTimer += Time.deltaTime;
            
            // Check if target has moved significantly
            float targetMovement = Vector3.Distance(target.position, lastTargetPosition);
            if (targetMovement > 1f || waitTimer >= waitTime)
            {
                isWaiting = false;
                waitTimer = 0f;
            }
        }
    }
    
    void FaceTarget()
    {
        Vector3 direction = (target.position - transform.position).normalized;
        direction.y = 0; // Keep on horizontal plane
        
        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }
    }
    
    void TeleportToTarget()
    {
        Vector3 teleportPosition = target.position + Vector3.back * followDistance;
        
        // Try to find a valid position on NavMesh
        if (useNavMesh)
        {
            NavMeshHit hit;
            if (NavMesh.SamplePosition(teleportPosition, out hit, 5f, NavMesh.AllAreas))
            {
                transform.position = hit.position;
            }
            else
            {
                transform.position = teleportPosition;
            }
        }
        else
        {
            transform.position = teleportPosition;
        }
        
        Debug.Log($"{gameObject.name} teleported to player.");
    }
    
    void FindPlayer()
    {
        // Try to find player by tag
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            target = player.transform;
            Debug.Log($"{gameObject.name}: Found player by tag.");
            return;
        }
        
        // Try to find FirstPersonController
        FirstPersonController fpsController = FindObjectOfType<FirstPersonController>();
        if (fpsController != null)
        {
            target = fpsController.transform;
            Debug.Log($"{gameObject.name}: Found FirstPersonController.");
            return;
        }
        
        // Try to find by name
        player = GameObject.Find("Player");
        if (player != null)
        {
            target = player.transform;
            Debug.Log($"{gameObject.name}: Found player by name.");
            return;
        }
        
        Debug.LogWarning($"{gameObject.name}: Could not find player automatically.");
    }
    
    // Public methods for runtime control
    public void SetTarget(Transform newTarget)
    {
        target = newTarget;
        if (target != null)
        {
            lastTargetPosition = target.position;
        }
    }
    
    public void SetFollowDistance(float distance)
    {
        followDistance = distance;
    }
    
    public void SetSpeed(float speed)
    {
        if (agent != null)
        {
            agent.speed = speed;
        }
    }
    
    public void StopFollowing()
    {
        if (agent != null)
        {
            agent.ResetPath();
        }
        isWaiting = true;
    }
    
    public void ResumeFollowing()
    {
        isWaiting = false;
        waitTimer = 0f;
    }
    
    // Gizmos for debugging
    void OnDrawGizmosSelected()
    {
        if (target != null)
        {
            // Draw connection line
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(transform.position, target.position);
            
            // Draw follow distance
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(target.position, followDistance);
            
            // Draw stop distance
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(target.position, stopDistance);
            
            // Draw teleport distance
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(target.position, teleportDistance);
            
            // Draw current path if using NavMesh
            if (useNavMesh && agent != null && agent.hasPath)
            {
                Gizmos.color = Color.cyan;
                Vector3[] path = agent.path.corners;
                for (int i = 0; i < path.Length - 1; i++)
                {
                    Gizmos.DrawLine(path[i], path[i + 1]);
                }
            }
        }
    }
}

using UnityEngine;
using Cinemachine;

public class CameraFollowScript : MonoBehaviour
{
    [Header("Camera Target Settings")]
    public Transform cameraTarget;
    public bool autoFindCamera = true;
    public bool followMainCamera = true;
    public bool followCinemachineCamera = true;
    
    [Header("Follow Behavior")]
    public FollowMode followMode = FollowMode.SmoothFollow;
    public Vector3 offset = new Vector3(0, 0, -2);
    public float followSpeed = 5f;
    public float rotationSpeed = 3f;
    
    [Header("Follow Options")]
    public bool followPosition = true;
    public bool followRotation = false;
    public bool maintainWorldOffset = false; // If true, offset is in world space
    public bool lookAtCamera = false; // Look at camera instead of following rotation
    
    [Header("Distance Control")]
    public bool useDistanceConstraints = true;
    public float minDistance = 1f;
    public float maxDistance = 10f;
    public float snapDistance = 15f; // Distance at which to snap to camera
    
    [Header("Advanced Settings")]
    public bool smoothDamping = true;
    public float dampingTime = 0.3f;
    public bool avoidObstacles = false;
    public LayerMask obstacleLayer = -1;
    
    public enum FollowMode
    {
        Instant,        // Immediately follow camera
        SmoothFollow,   // Smooth interpolation
        SmoothDamp,     // Smooth damping
        Elastic,        // Elastic/spring-like movement
        Delayed         // Follow with delay
    }
    
    private Camera mainCamera;
    private CinemachineVirtualCameraBase virtualCamera;
    private CinemachineBrain cinemachineBrain;
    private Vector3 velocity = Vector3.zero;
    private Vector3 angularVelocity = Vector3.zero;
    private Vector3 lastCameraPosition;
    private Quaternion lastCameraRotation;
    
    void Start()
    {
        if (cameraTarget == null && autoFindCamera)
        {
            FindCamera();
        }
        
        if (cameraTarget != null)
        {
            lastCameraPosition = cameraTarget.position;
            lastCameraRotation = cameraTarget.rotation;
        }
    }
    
    void LateUpdate()
    {
        if (cameraTarget == null)
        {
            if (autoFindCamera)
            {
                FindCamera();
            }
            return;
        }
        
        UpdateFollowBehavior();
        
        // Update last known camera state
        lastCameraPosition = cameraTarget.position;
        lastCameraRotation = cameraTarget.rotation;
    }
    
    void UpdateFollowBehavior()
    {
        if (followPosition)
        {
            UpdatePosition();
        }
        
        if (followRotation && !lookAtCamera)
        {
            UpdateRotation();
        }
        else if (lookAtCamera)
        {
            LookAtCameraTarget();
        }
    }
    
    void UpdatePosition()
    {
        Vector3 targetOffset = maintainWorldOffset ? offset : cameraTarget.TransformDirection(offset);
        Vector3 desiredPosition = cameraTarget.position + targetOffset;
        
        // Apply distance constraints
        if (useDistanceConstraints)
        {
            float distance = Vector3.Distance(transform.position, cameraTarget.position);
            
            // Snap to camera if too far
            if (distance > snapDistance)
            {
                transform.position = desiredPosition;
                return;
            }
            
            // Don't move closer if too close
            if (distance < minDistance)
            {
                Vector3 direction = (transform.position - cameraTarget.position).normalized;
                transform.position = cameraTarget.position + direction * minDistance;
                return;
            }
            
            // Don't move further if at max distance
            if (distance > maxDistance)
            {
                Vector3 direction = (desiredPosition - cameraTarget.position).normalized;
                desiredPosition = cameraTarget.position + direction * maxDistance;
            }
        }
        
        // Handle obstacle avoidance
        if (avoidObstacles)
        {
            desiredPosition = CheckForObstacles(desiredPosition);
        }
        
        // Apply movement based on follow mode
        ApplyMovement(desiredPosition);
    }
    
    void ApplyMovement(Vector3 desiredPosition)
    {
        switch (followMode)
        {
            case FollowMode.Instant:
                transform.position = desiredPosition;
                break;
                
            case FollowMode.SmoothFollow:
                transform.position = Vector3.Lerp(transform.position, desiredPosition, followSpeed * Time.deltaTime);
                break;
                
            case FollowMode.SmoothDamp:
                if (smoothDamping)
                {
                    transform.position = Vector3.SmoothDamp(transform.position, desiredPosition, ref velocity, dampingTime);
                }
                else
                {
                    transform.position = Vector3.SmoothDamp(transform.position, desiredPosition, ref velocity, 1f / followSpeed);
                }
                break;
                
            case FollowMode.Elastic:
                Vector3 direction = desiredPosition - transform.position;
                float distance = direction.magnitude;
                if (distance > 0.1f)
                {
                    Vector3 force = direction * followSpeed * distance * 0.1f;
                    transform.position += force * Time.deltaTime;
                }
                break;
                
            case FollowMode.Delayed:
                // Add some delay to the movement
                Vector3 delayedPosition = Vector3.Lerp(lastCameraPosition, cameraTarget.position, 0.8f);
                Vector3 targetOffset = maintainWorldOffset ? offset : cameraTarget.TransformDirection(offset);
                Vector3 finalPosition = delayedPosition + targetOffset;
                transform.position = Vector3.Lerp(transform.position, finalPosition, followSpeed * Time.deltaTime);
                break;
        }
    }
    
    void UpdateRotation()
    {
        Quaternion desiredRotation = cameraTarget.rotation;
        
        switch (followMode)
        {
            case FollowMode.Instant:
                transform.rotation = desiredRotation;
                break;
                
            case FollowMode.SmoothFollow:
            case FollowMode.Elastic:
                transform.rotation = Quaternion.Slerp(transform.rotation, desiredRotation, rotationSpeed * Time.deltaTime);
                break;
                
            case FollowMode.SmoothDamp:
                // Convert to euler for smooth damping
                Vector3 currentEuler = transform.rotation.eulerAngles;
                Vector3 targetEuler = desiredRotation.eulerAngles;
                Vector3 smoothedEuler = new Vector3(
                    Mathf.SmoothDampAngle(currentEuler.x, targetEuler.x, ref angularVelocity.x, dampingTime),
                    Mathf.SmoothDampAngle(currentEuler.y, targetEuler.y, ref angularVelocity.y, dampingTime),
                    Mathf.SmoothDampAngle(currentEuler.z, targetEuler.z, ref angularVelocity.z, dampingTime)
                );
                transform.rotation = Quaternion.Euler(smoothedEuler);
                break;
                
            case FollowMode.Delayed:
                Quaternion delayedRotation = Quaternion.Slerp(lastCameraRotation, cameraTarget.rotation, 0.8f);
                transform.rotation = Quaternion.Slerp(transform.rotation, delayedRotation, rotationSpeed * Time.deltaTime);
                break;
        }
    }
    
    void LookAtCameraTarget()
    {
        Vector3 direction = (cameraTarget.position - transform.position).normalized;
        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }
    }
    
    Vector3 CheckForObstacles(Vector3 desiredPosition)
    {
        Vector3 directionToCamera = (desiredPosition - cameraTarget.position).normalized;
        float distanceToCamera = Vector3.Distance(cameraTarget.position, desiredPosition);
        
        RaycastHit hit;
        if (Physics.Raycast(cameraTarget.position, directionToCamera, out hit, distanceToCamera, obstacleLayer))
        {
            // Move to a safe position slightly in front of the obstacle
            return hit.point - directionToCamera * 0.5f;
        }
        
        return desiredPosition;
    }
    
    void FindCamera()
    {
        // First try to find the main camera
        if (followMainCamera)
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                GameObject cameraObj = GameObject.FindGameObjectWithTag("MainCamera");
                if (cameraObj != null)
                {
                    mainCamera = cameraObj.GetComponent<Camera>();
                }
            }
            
            if (mainCamera != null)
            {
                cameraTarget = mainCamera.transform;
                Debug.Log($"{gameObject.name}: Found main camera.");
                
                // Check if it has Cinemachine Brain
                cinemachineBrain = mainCamera.GetComponent<CinemachineBrain>();
                return;
            }
        }
        
        // Try to find Cinemachine virtual camera
        if (followCinemachineCamera)
        {
            virtualCamera = FindObjectOfType<CinemachineVirtualCameraBase>();
            if (virtualCamera != null)
            {
                cameraTarget = virtualCamera.transform;
                Debug.Log($"{gameObject.name}: Found Cinemachine virtual camera.");
                return;
            }
        }
        
        Debug.LogWarning($"{gameObject.name}: Could not find camera automatically. Please assign camera target manually.");
    }
    
    // Public methods for runtime control
    public void SetCameraTarget(Transform newTarget)
    {
        cameraTarget = newTarget;
        if (cameraTarget != null)
        {
            lastCameraPosition = cameraTarget.position;
            lastCameraRotation = cameraTarget.rotation;
        }
    }
    
    public void SetFollowMode(FollowMode newMode)
    {
        followMode = newMode;
    }
    
    public void SetOffset(Vector3 newOffset)
    {
        offset = newOffset;
    }
    
    public void SetFollowSpeed(float speed)
    {
        followSpeed = speed;
    }
    
    // Gizmos for debugging
    void OnDrawGizmosSelected()
    {
        if (cameraTarget != null)
        {
            // Draw connection line
            Gizmos.color = Color.cyan;
            Gizmos.DrawLine(transform.position, cameraTarget.position);
            
            // Draw desired position
            Vector3 targetOffset = maintainWorldOffset ? offset : cameraTarget.TransformDirection(offset);
            Vector3 desiredPos = cameraTarget.position + targetOffset;
            
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(desiredPos, 0.3f);
            
            // Draw distance constraints
            if (useDistanceConstraints)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(cameraTarget.position, minDistance);
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(cameraTarget.position, maxDistance);
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(cameraTarget.position, snapDistance);
            }
            
            // Draw offset direction
            Gizmos.color = Color.magenta;
            Gizmos.DrawRay(cameraTarget.position, targetOffset);
        }
    }
}

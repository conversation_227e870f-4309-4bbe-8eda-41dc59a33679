%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: BranchesPoplar001_ALP
  m_Shader: {fileID: 4800000, guid: eee2400ecc6f042448341f68f711a01e, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: f0ee2b666bf7b4943a6415517ca86f94, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorShiftMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlossMap:
        m_Texture: {fileID: 2800000, guid: 74714adb81a42a44c92840dad8686ae2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: d7e1bc1cf7e548c42ab1a3e87bb7a549, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: cde5cb8940f6c8049b7d9963c5c420d3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadowOffset:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SmoothnessMap:
        m_Texture: {fileID: 2800000, guid: 4c2edc86d0c3e21418e208758a0f3f88, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TranslucencyMap:
        m_Texture: {fileID: 2800000, guid: efe0a80542b66a742bdc1f4b97b7996a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TransmissionMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoffBias: 0.5
    - _AlphaRemapMax: 1
    - _AlphaRemapMin: 0.086206906
    - _Brightness: 0.65
    - _BumpScale: 1
    - _CATEGORY_ALPHACLIPPING: 1
    - _CATEGORY_COLOR: 1
    - _CATEGORY_COLORSHIFT: 0
    - _CATEGORY_SURFACEINPUTS: 1
    - _CATEGORY_TRANSLUCENCY: 1
    - _CATEGORY_TRANSLUCENCYASE: 1
    - _CATEGORY_TRANSMISSION: 0
    - _CATEGORY_WIND: 1
    - _ColorShiftEnable: 0
    - _ColorShiftEnableMask: 0
    - _ColorShiftInfluence: 0.427
    - _ColorShiftMaskFuzziness: 0.25
    - _ColorShiftMaskInverted: 0
    - _ColorShiftNoiseScale: 1.094
    - _ColorShiftSaturation: 0.794
    - _ColorShiftSource: 0
    - _ColorShiftVariation: 0.529
    - _ColorShiftVariationRGB: 0.093
    - _ColorShiftWorldSpaceDistance: 5
    - _ColorShiftWorldSpaceNoiseShift: 5
    - _ColorShiftWorldSpaceOffset: 1
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DoubleSidedNormalMode: 1
    - _DstBlend: 0
    - _GlancingClipMode: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Metallic: 0
    - _MetallicStrength: 0
    - _Mode: 0
    - _NormalStrength: 1.5
    - _OcclusionSource: 0
    - _OcclusionStrength: 1
    - _OcclusionStrengthAO: 0.1
    - _Parallax: 0.02
    - _SPACE_ALPHACLIP: 0
    - _SPACE_COLOR: 0
    - _SPACE_COLORSHIFT: 0
    - _SPACE_SURFACEINPUTS: 0
    - _SPACE_TRANSLUCENCY: 0
    - _SPACE_TRANSLUCENCYASE: 0
    - _SPACE_TRANSMISSION: 0
    - _SPACE_WIND: 0
    - _Shininess: 0.8
    - _SmoothnessFresnelEnable: 0
    - _SmoothnessFresnelPower: 10
    - _SmoothnessFresnelScale: 1.1
    - _SmoothnessSource: 0
    - _SmoothnessStrength: 0.585
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SquashAmount: 1
    - _SrcBlend: 1
    - _ThicknessFeather: 1
    - _ThicknessMapInverted: 0
    - _ThicknessStrength: 1.466198
    - _TransAmbient: 0.5
    - _TransDirect: 0.266
    - _TransNormalDistortion: 0
    - _TransScattering: 6
    - _TransShadow: 0.677
    - _Translucency: 0.6
    - _TranslucencyEnable: 1
    - _TranslucencySource: 0
    - _TranslucencyStrength: 1.5
    - _TransmissionEnable: 1
    - _TransmissionMaskFeather: 1
    - _TransmissionMaskInverted: 0
    - _TransmissionMaskStrength: 1.466198
    - _TransmissionSource: 0
    - _TransmissionStrength: 1.6
    - _UVMode: 0
    - _UVSec: 0
    - _WindEnable: 1
    - _WindEnableMode: 0
    - _WindEnableType: 1
    - _WindGlobalIntensity: 1
    - _WindGlobalTurbulence: 0.5
    - _WindLocalDirection: 0
    - _WindLocalIntensity: 1
    - _WindLocalPulseFrequency: 0.1
    - _WindLocalRandomOffset: 0.2
    - _WindLocalTurbulence: 0.35
    - _ZWrite: 1
    - __dirty: 1
    m_Colors:
    - _AlphaRemap: {r: 0.086206906, g: 1, b: 0, a: 0}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MainUVs: {r: 1, g: 1, b: 0, a: 0}
    - _TranslucencyColor: {r: 0.7807148, g: 0.90588236, b: 0.2509804, a: 1}
    - _TransmissionColor: {r: 0.920229, g: 1, b: 0.6650944, a: 1}
    - _TreeInstanceColor: {r: 1, g: 1, b: 1, a: 1}
    - _TreeInstanceScale: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
